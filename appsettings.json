{"Alpaca": {"ApiKey": "ENC:QUtSNlNMSUtTQjBOQ0JMMkNOTEI=", "SecretKey": "ENC:bWdSdzAyZDVYTmFiY1Vnb3BWbWIyMmZEb0NFVkxzanM3UXN3eXdKeg==", "BaseUrl": "https://api.alpaca.markets", "DataUrl": "https://data.alpaca.markets"}, "Polygon": {"ApiKey": "********************************", "BaseUrl": "https://api.polygon.io"}, "Trading": {"PrimarySymbol": "SPX", "BackupSymbol": "SPY", "MaxPositionSize": 1000, "MaxDailyLoss": 150, "RiskPerTrade": 0.01, "MaxPositionsPerDay": 2, "MinDaysToExpiration": 0, "MaxDaysToExpiration": 0, "EntryTimeStart": "09:45:00", "EntryTimeEnd": "10:30:00", "ManagementTime": "14:00:00", "ForceCloseTime": "15:45:00", "TradingEndTime": "16:00:00", "MinAccountEquity": 10000, "MinBuyingPower": 5000, "ProfitTargetPercent": 0.5, "StopLossPercent": 2.0, "RiskRewardThreshold": 0.15, "MaxPortfolioHeat": 0.05}, "MarketRegime": {"VixLowThreshold": 25, "VixHighThreshold": 55, "TrendLookbackPeriods": 20, "VolatilityLookbackPeriods": 14, "VolatilityCalculationDays": 30, "VolatilityForecastLookback": 60, "ATRPeriods": 14, "RSIPeriods": 14, "BollingerBandPeriods": 20, "BollingerBandStdDev": 2.0, "GarchAlpha": 0.1, "GarchBeta": 0.85, "GarchOmega": 1e-05, "MicrostructureLookbackHours": 6, "MultiTimeframeEnabled": true, "RegimeTransitionSensitivity": 0.7, "VolatilitySpikeThreshold": 1.5, "CorrelationBreakdownThreshold": 0.3, "UnusualActivityVolumeThreshold": 5.0, "SentimentExtremeThreshold": 60, "MarketBreadthLookbackDays": 20, "OptionsFlowLookbackHours": 4, "AllowHighVolatilityTrading": true, "HighVolatilityMaxPositions": 1, "HighVolatilityRiskReduction": 0.5}, "DataSources": {"Note": "Data source hierarchy: Polygon (primary) → Polygon Markets (secondary) → Alpaca/ETF Proxy (tertiary)", "GlobalHierarchy": [{"Source": "Polygon", "Priority": 1, "Description": "Real index data from Polygon.io Indices Starter subscription"}, {"Source": "PolygonMarkets", "Priority": 2, "Description": "Alternative Polygon.io market data endpoints"}, {"Source": "AlpacaETF", "Priority": 3, "Description": "Alpaca ETF proxies and tradeable securities as tertiary fallback"}], "IndexMappings": {"VIX": {"Primary": "I:VIX", "Secondary": "I:VIX", "Tertiary": "SyntheticVIX", "Description": "CBOE Volatility Index"}, "SPX": {"Primary": "I:SPX", "Secondary": "I:SPX", "Tertiary": "SPY", "Description": "S&P 500 Index"}, "NDX": {"Primary": "I:NDX", "Secondary": "I:NDX", "Tertiary": "QQQ", "Description": "NASDAQ 100 Index"}, "RUT": {"Primary": "I:RUT", "Secondary": "I:RUT", "Tertiary": "IWM", "Description": "Russell 2000 Index"}, "DJI": {"Primary": "I:DJI", "Secondary": "I:DJI", "Tertiary": "DIA", "Description": "Dow Jones Industrial Average"}}, "SectorETFs": {"Note": "Major sector ETFs for market context and breadth analysis", "Sectors": {"XLF": {"Name": "Financial Select Sector SPDR Fund", "Sector": "Financials", "Weight": 0.25, "Description": "Banking, insurance, real estate, and financial services"}, "XLK": {"Name": "Technology Select Sector SPDR Fund", "Sector": "Technology", "Weight": 0.3, "Description": "Software, hardware, semiconductors, and tech services"}, "XLE": {"Name": "Energy Select Sector SPDR Fund", "Sector": "Energy", "Weight": 0.15, "Description": "Oil, gas, renewable energy, and energy equipment"}, "XLV": {"Name": "Health Care Select Sector SPDR Fund", "Sector": "Healthcare", "Weight": 0.2, "Description": "Pharmaceuticals, biotechnology, and healthcare equipment"}, "XLI": {"Name": "Industrial Select Sector SPDR Fund", "Sector": "Industrials", "Weight": 0.1, "Description": "Manufacturing, aerospace, defense, and transportation"}}, "DataSources": {"Primary": "Alpaca", "Secondary": "Polygon", "Tertiary": "Fallback"}}}, "SyntheticVix": {"Note": "Tertiary fallback VIX calculation using ETF proxies when Polygon sources unavailable.", "SubstituteIndices": [{"Symbol": "VXX", "Type": "ETF", "Weight": 0.5, "Source": "alpaca"}, {"Symbol": "UVXY", "Type": "ETF", "Weight": 0.3, "Source": "alpaca"}, {"Symbol": "SVXY", "Type": "ETF", "Weight": -0.2, "Source": "alpaca"}], "Normalization": {"Method": "z-score", "Window": 20}, "CompositeIndexLabel": "SyntheticVIX", "Usage": "Tertiary fallback when Polygon and Polygon Markets VIX sources are unavailable."}, "VixCalibration": {"Note": "Configuration for calibrating synthetic VIX against real Polygon VIX data", "IntervalMinutes": 30, "RecalibrationThreshold": 0.15, "MaxHistoryPoints": 1000, "MinDataPointsForCalibration": 10, "CorrelationThreshold": 0.7, "BiasThreshold": 2.0, "ScalingFactorRange": {"Min": 0.8, "Max": 1.2}, "AutoCalibrationEnabled": true, "CalibrationReportIntervalHours": 6, "AlertOnSignificantDrift": true, "DriftThresholdPercent": 20.0}, "Risk": {"MaxDrawdown": 0.05, "VaRLimit": 0.02, "MaxConcentration": 0.5, "MaxCorrelatedExposure": 0.6, "PortfolioHeatLimit": 0.6, "MaxDailyTrades": 3, "MaxOpenPositions": 3, "StressTestMultiplier": 2.0, "RiskRewardMinimum": 0.2, "MaxPositionsPerSymbol": 2, "ConcentrationWarningLevel": 0.4}, "Strategies": {"PutCreditSpread": {"Enabled": true, "Priority": 1, "MinDelta": 0.05, "MaxDelta": 0.15, "MinPremium": 0.1, "MaxSpreadWidth": 10, "MinDaysToExpiration": 0, "MaxDaysToExpiration": 0, "ProfitTarget": 0.5, "StopLoss": 2.0}, "IronButterfly": {"Enabled": true, "Priority": 2, "ATMRange": 0.02, "WingWidth": 25, "MinPremium": 0.15, "ProfitTarget": 0.5, "StopLoss": 2.0}, "CallCreditSpread": {"Enabled": true, "Priority": 3, "MinDelta": 0.05, "MaxDelta": 0.15, "MinPremium": 0.1, "MaxSpreadWidth": 10, "ProfitTarget": 0.5, "StopLoss": 2.0}, "IronCondor": {"Enabled": true, "Priority": 2, "MinDelta": 0.05, "MaxDelta": 0.1, "MinPremium": 0.3, "WingWidth": 15, "ProfitTarget": 0.5, "StopLoss": 2.0}, "BrokenWingButterfly": {"Enabled": true, "Priority": 4, "MinPremium": 0.5, "ProfitTarget": 0.6, "StopLoss": 2.0}}, "MachineLearning": {"ModelUpdateIntervalHours": 24, "MinTrainingDataPoints": 100, "ConfidenceThreshold": 0.7, "SignalQualityWeights": {"ML": 0.4, "Technical": 0.3, "MarketCondition": 0.2, "Liquidity": 0.1}, "PredictionTimeframes": {"PriceDirection": "1h", "Volatility": "4h"}}, "Monitoring": {"UpdateIntervalMs": 2000, "AlertCheckIntervalMs": 5000, "HealthCheckIntervalMs": 15000, "MaxMetricsHistory": 1000, "NotificationChannels": {"Console": {"Enabled": true, "Priority": 1}, "Email": {"Enabled": true, "Priority": 2, "SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "your-app-password", "ToAddress": "<EMAIL>", "UseSsl": true}, "SMS": {"Enabled": false, "Priority": 1, "Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "", "AuthToken": "", "FromNumber": "", "ToNumber": "", "Region": ""}, "Slack": {"Enabled": false, "Priority": 3, "WebhookUrl": "", "Channel": "#alerts", "Username": "Zero DTE Bot", "IconEmoji": ":warning:"}, "Discord": {"Enabled": true, "Priority": 2, "BotToken": "MTM4MjU0NDg4MzI4NTQ5NTgwOA.G2atad.JHbCXK2yhoH4ENJC58u262lPh241Q4Vvf4xI9c", "ChannelId": 1382148371103350799, "WebhookUrl": "", "Username": "AugmentBot", "AvatarUrl": "", "UseEmbeds": true, "EnableSlashCommands": true}}}, "GuidanceRequest": {"Enabled": true, "ChatGptBotMention": "@ChatGptBot", "ChatGptBotUserId": "", "DefaultTimeout": "00:00:30", "DefaultMaxRetries": 3, "StoreResponses": true, "ResponseStoragePath": "GuidanceResponses", "LogRequests": true, "EnableRetryLogic": true}, "OpenAI": {"Enabled": true, "ApiKey": "***********************************************************************************************************************************************************************", "Model": "gpt-4o", "MaxTokens": 2000, "Temperature": 0.7, "SystemPrompt": "You are <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a developer assistant who returns clear, well-structured technical instructions in response to requests from Augment. You are helping with a C# trading application called ZeroDateStrat that uses Alpaca API for 0 DTE options trading strategies.", "TriggerKeywords": ["!askchatgpt", "!chatgpt", "!gpt"], "EnableMentionTrigger": true, "EnableKeywordTrigger": true, "ResponsePrefix": "🤖 **ChatGPT Response:**\n", "ErrorMessage": "❌ Something went wrong while fetching the response. Please try again.", "EmptyRequestMessage": "⚠️ Please provide a valid request after the mention or command.", "MaxMessageLength": 2000}, "ChatGPTBot": {"Enabled": true, "BotToken": "MTM4MjU0Mjc1NDM5OTEyOTcwMQ.GCzAVF.A2k7njYELsRSw0_zF2JK0uu36L7b6mbSVW-0Z8", "ChannelId": 1382148371103350799, "Username": "ChatGPTBot", "EnableMentionTrigger": true, "EnableKeywordTrigger": true, "TriggerKeywords": ["!askchatgpt", "!chatgpt", "!gpt"], "ResponsePrefix": "🤖 **ChatGPT Response:**\n", "ErrorMessage": "⚠️ ChatGPTBot encountered an issue with the request. Try again in a few moments.", "EmptyRequestMessage": "⚠️ Please provide a valid request after the mention or command.", "MaxMessageLength": 2000, "EnablePriorityTagging": true, "EnableResponsePagination": true}, "CircuitBreaker": {"AlpacaAPI": {"FailureThreshold": 5, "TimeoutMinutes": 5}, "OptionsData": {"FailureThreshold": 3, "TimeoutMinutes": 3}, "MarketData": {"FailureThreshold": 3, "TimeoutMinutes": 2}, "RiskManagement": {"FailureThreshold": 2, "TimeoutMinutes": 1}, "OrderExecution": {"FailureThreshold": 2, "TimeoutMinutes": 1}}, "Optimization": {"MinIntervalHours": 24, "MinWinRate": 0.6, "MinSharpe": 1.0, "MaxDrawdown": 0.1, "PortfolioOptimization": {"Enabled": true, "RebalanceIntervalHours": 168, "MaxAllocationPerStrategy": 0.6, "MinAllocationPerStrategy": 0.1}, "AdaptiveParameters": {"Enabled": true, "UpdateIntervalHours": 12, "PerformanceThreshold": 0.05}}, "MultiTimeframe": {"Enabled": true, "Timeframes": ["1m", "5m", "15m", "1h", "1d"], "CacheExpiryMinutes": 5, "ConflictResolution": "HigherTimeframePriority"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "ZeroDateStrat": "Debug", "ZeroDateStrat.Services.AlpacaService": "Debug", "ZeroDateStrat.Services.RiskManager": "Debug", "ZeroDateStrat.Services.AlpacaVixService": "Debug", "ZeroDateStrat.Services.PolygonDataService": "Debug", "ZeroDateStrat.Services.SyntheticVixService": "Debug", "ZeroDateStrat.Services.VixCalibrationService": "Debug", "ZeroDateStrat.Strategies": "Debug"}}}